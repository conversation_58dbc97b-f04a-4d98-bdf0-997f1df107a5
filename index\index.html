<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shree Kripa Enterprises - Electricity Complaint Management System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .hidden {
            display: none;
        }
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin: 20px 0;
        }
        .btn {
            padding: 10px 20px;
            border-radius: 5px;
            border: none;
            cursor: pointer;
            transition: 0.3s;
        }
        .btn-primary {
            background-color: #3b82f6;
            color: white;
        }
        .btn-primary:hover {
            background-color: #2563eb;
        }
        .btn-secondary {
            background-color: #6b7280;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #4b5563;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .table th, .table td {
            padding: 12px;
            border: 1px solid #e5e7eb;
            text-align: left;
        }
        .table th {
            background-color: #f3f4f6;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 5px;
            font-size: 16px;
        }
        .hero-image {
            width: 100%;
            max-width: 1600px;
            height: auto;
            border-radius: 8px;
        }
        .navbar {
            background-color: #1f2937;
            color: white;
            padding: 10px 0;
        }
        .navbar a {
            color: white;
            text-decoration: none;
            margin: 0 15px;
        }
    </style>
</head>
<body>
    <div id="navbar" class="navbar">
        <div class="container">
            <div class="flex justify-between items-center">
                <h1>Shree Kripa Enterprises</h1>
                <div>
                    <a href="#" id="home-link">Home</a>
                    <a href="#" id="login-link">Login</a>
                    <a href="#" id="register-link">Register</a>
                    <a href="#" id="customer-link" class="hidden">Customer Dashboard</a>
                    <a href="#" id="admin-link" class="hidden">Admin Dashboard</a>
                    <a href="#" id="logout-link" class="hidden">Logout</a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <section id="home">
            <div class="hero-image">
                <img src="https://storage.googleapis.com/workspace-0f70711f-8b4e-4d94-86f1-2a93ccde5887/image/88829b96-da97-41a8-9a52-222206c244ae.png" alt="Modern electricity management center with engineers in a control room monitoring screens showing power grid data, transformers, and circuit diagrams in a futuristic setting with blue and green lighting" />
            </div>
            <p class="text-center text-white text-lg mt-4">Welcome to the Electricity Complaint Management System. Lodgge complaints or manage them efficiently.</p>
        </section>

        <section id="login" class="hidden">
            <div class="card">
                <h2>Login</h2>
                <form id="login-form">
                    <div class="form-group">
                        <label>Email</label>
                        <input type="email" id="login-email" required>
                    </div>
                    <div class="form-group">
                        <label>Password</label>
                        <input type="password" id="login-password" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Login</button>
                </form>
            </div>
        </section>

        <section id="register" class="hidden">
            <div class="card">
                <h2>Register</h2>
                <form id="register-form">
                    <div class="form-group">
                        <label>Name</label>
                        <input type="text" id="register-name" required>
                    </div>
                    <div class="form-group">
                        <label>Email</label>
                        <input type="email" id="register-email" required>
                    </div>
                    <div class="form-group">
                        <label>Password</label>
                        <input type="password" id="register-password" required>
                    </div>
                    <div class="form-group">
                        <label>Role</label>
                        <select id="register-role">
                            <option value="customer">Customer</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary">Register</button>
                </form>
            </div>
        </section>

        <section id="customer-dashboard" class="hidden">
            <div class="card">
                <h2>Welcome, Customer</h2>
                <button id="lodge-complaint-btn" class="btn btn-primary mb-4">Lodge a Complaint</button>
                <h3>Your Complaints</h3>
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Type</th>
                            <th>Description</th>
                            <th>Status</th>
                            <th>Created</th>
                        </tr>
                    </thead>
                    <tbody id="customer-complaints"></tbody>
                </table>
            </div>
        </section>

        <section id="lodge-complaint" class="hidden">
            <div class="card">
                <h2>Lodge a Complaint</h2>
                <form id="complaint-form">
                    <div class="form-group">
                        <label>Type</label>
                        <select id="complaint-type">
                            <option value="Power Cut">Power Cut</option>
                            <option value="Meter Issue">Meter Issue</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Description</label>
                        <textarea id="complaint-description" required></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">Submit</button>
                </form>
            </div>
        </section>

        <section id="admin-dashboard" class="hidden">
            <div class="card">
                <h2>Welcome, Admin</h2>
                <h3>All Complaints</h3>
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>User</th>
                            <th>Type</th>
                            <th>Description</th>
                            <th>Status</th>
                            <th>Assigned To</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody id="admin-complaints"></tbody>
                </table>
            </div>
        </section>
    </div>

    <script>
        // Simulated backend using localStorage
        let users = JSON.parse(localStorage.getItem('users')) || [];
        let complaints = JSON.parse(localStorage.getItem('complaints')) || [];
        let technicians = [
            { id: 1, name: 'John Doe', contact: '<EMAIL>' },
            { id: 2, name: 'Jane Smith', contact: '<EMAIL>' }
        ];
        let currentUser = localStorage.getItem('currentUser') || null;

        function saveData() {
            localStorage.setItem('users', JSON.stringify(users));
            localStorage.setItem('complaints', JSON.stringify(complaints));
        }

        // Navigation
        document.getElementById('home-link').addEventListener('click', () => showSection('home'));
        document.getElementById('login-link').addEventListener('click', () => showSection('login'));
        document.getElementById('register-link').addEventListener('click', () => showSection('register'));
        document.getElementById('customer-link').addEventListener('click', () => showSection('customer-dashboard'));
        document.getElementById('admin-link').addEventListener('click', () => showSection('admin-dashboard'));
        document.getElementById('logout-link').addEventListener('click', () => {
            currentUser = null;
            localStorage.removeItem('currentUser');
            updateNavbar();
            showSection('home');
        });

        function showSection(sectionId) {
            document.querySelectorAll('section').forEach(sec => sec.classList.add('hidden'));
            document.getElementById(sectionId).classList.remove('hidden');
        }

        function updateNavbar() {
            const isLoggedIn = !!currentUser;
            const isAdmin = currentUser && JSON.parse(currentUser).role === 'admin';
            document.getElementById('login-link').style.display = isLoggedIn ? 'none' : 'inline';
            document.getElementById('register-link').style.display = isLoggedIn ? 'none' : 'inline';
            document.getElementById('customer-link').style.display = isLoggedIn && !isAdmin ? 'inline' : 'none';
            document.getElementById('admin-link').style.display = isLoggedIn && isAdmin ? 'inline' : 'none';
            document.getElementById('logout-link').style.display = isLoggedIn ? 'inline' : 'none';
        }

        updateNavbar();
        if (currentUser) {
            const user = JSON.parse(currentUser);
            if (user.role === 'admin') {
                showSection('admin-dashboard');
            } else {
                showSection('customer-dashboard');
            }
        } else {
            showSection('home');
        }

        // Register
        document.getElementById('register-form').addEventListener('submit', (e) => {
            e.preventDefault();
            const name = document.getElementById('register-name').value;
            const email = document.getElementById('register-email').value;
            const password = document.getElementById('register-password').value;
            const role = document.getElementById('register-role').value;

            const existingUser = users.find(u => u.email === email);
            if (existingUser) {
                alert('User already exists');
                return;
            }

            users.push({ id: Date.now(), name, email, password, role });
            saveData();
            alert('Registered successfully');
            showSection('login');
        });

        // Login
document.getElementById('login-form').addEventListener('submit', (e) => {
    e.preventDefault();
    const email = document.getElementById('login-email').value;
    const password = document.getElementById('login-password').value;

    const user = users.find(u => u.email === email && u.password === password);
    if (!user) {
        alert('Invalid credentials');
        return;
    }

    currentUser = JSON.stringify(user);
    localStorage.setItem('currentUser', currentUser);
    updateNavbar();
    if (user.role === 'admin') {
        showSection('admin-dashboard');
        loadAdminComplaints();
    } else {
        showSection('customer-dashboard');
        loadCustomerComplaints();
    }
});

        // Lodge Complaint
        document.getElementById('lodge-complaint-btn').addEventListener('click', () => showSection('lodge-complaint'));
        document.getElementById('complaint-form').addEventListener('submit', (e) => {
            e.preventDefault();
            const type = document.getElementById('complaint-type').value;
            const description = document.getElementById('complaint-description').value;
            const user = JSON.parse(currentUser);

            complaints.push({
                id: Date.now(),
                userId: user.id,
                type,
                description,
                status: 'Pending',
                assignedTo: null,
                created: new Date().toISOString()
            });
            saveData();
            alert('Complaint lodged');
            loadCustomerComplaints();
            showSection('customer-dashboard');
        });

        function loadCustomerComplaints() {
            const user = JSON.parse(currentUser);
            const tbody = document.getElementById('customer-complaints');
            tbody.innerHTML = '';
            complaints.filter(c => c.userId === user.id).forEach(c => {
                const row = `<tr>
                    <td>${c.id}</td>
                    <td>${c.type}</td>
                    <td>${c.description}</td>
                    <td>${c.status}</td>
                    <td>${new Date(c.created).toLocaleDateString()}</td>
                </tr>`;
                tbody.innerHTML += row;
            });
        }

        function loadAdminComplaints() {
            const tbody = document.getElementById('admin-complaints');
            tbody.innerHTML = '';
            complaints.forEach(c => {
                const user = users.find(u => u.id === c.userId);
                const formattedComplaint = `
                    <tr>
                        <td>${c.id}</td>
                        <td>${user ? user.name : 'Unknown'}</td>
                        <td>${c.type}</td>
                        <td>${c.description}</td>
                        <td>${c.status}</td>
                        <td>${technicians.find(t => t.id === c.assignedTo)?.name || 'Not Assigned'}</td>
                        <td>
                            ${c.status === 'Pending' ? `<button class="btn btn-secondary" onclick="assignComplaint(${c.id})">Assign</button>` : ''}
                            ${['Pending', 'In Progress'].includes(c.status) ? `<button class="btn btn-primary" onclick="updateStatus(${c.id}, 'In Progress')">In Progress</button> <button class="btn btn-primary" onclick="updateStatus(${c.id}, 'Resolved')">Resolve</button>` : ''}
                        </td>
                    </tr>
                `;
                tbody.innerHTML += formattedComplaint;
            });
        }

        function assignComplaint(complaintId) {
            const assignment = prompt('Enter technician ID (1 for John Doe, 2 for Jane Smith)');
            const techId = parseInt(assignment);
            const complaint = complaints.find(c => c.id === complaintId);
            if (technicians.some(t => t.id === techId)) {
                complaint.assignedTo = techId;
                complaint.status = 'In Progress';
                saveData();
                loadAdminComplaints();
            } else {
                alert('Invalid technician ID');
            }
        }

        function updateStatus(complaintId, status) {
            const complaint = complaints.find(c => c.id === complaintId);
            complaint.status = status;
            saveData();
            loadAdminComplaints();
        }
    </script>
</body>
</html>
</content>
</create_file>
